-- Criar banco de dados se não existir
CREATE DATABASE IF NOT EXISTS meu_banco;
USE meu_banco;

-- <PERSON>riar tabela de usuários
CREATE TABLE IF NOT EXISTS minha_tabela (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Inserir alguns dados de exemplo
INSERT INTO minha_tabela (nome, email) VALUES 
('<PERSON>', '<EMAIL>'),
('<PERSON>', '<EMAIL>'),
('<PERSON>', '<EMAIL>')
ON DUPLICATE KEY UPDATE nome=VALUES(nome);
