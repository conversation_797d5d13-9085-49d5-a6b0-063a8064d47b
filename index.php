<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulário de Usuários</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        form { margin-bottom: 20px; }
        input, button { padding: 10px; margin: 5px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid black; padding: 8px; text-align: left; }
    </style>
</head>
<body>
    <h2>Lista de Usuários</h2>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Nome</th>
                <th>Email</th>
            </tr>
        </thead>
        <tbody id="userList">
            <!-- Os usuários serão preenchidos via JavaScript -->
        </tbody>
    </table>

    <h2>Adicionar Usuário</h2>
    <form id="userForm">
        <input type="text" name="nome" placeholder="Nome" required>
        <input type="email" name="email" placeholder="Email" required>
        <button type="submit">Adicionar</button>
    </form>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const apiUrl = "http://localhost/seu_api.php"; // Substitua pelo caminho real da API

            // Carregar usuários
            function carregarUsuarios() {
                fetch(apiUrl)
                    .then(response => response.json())
                    .then(data => {
                        const userList = document.getElementById("userList");
                        userList.innerHTML = "";
                        data.forEach(usuario => {
                            userList.innerHTML += `<tr>
                                <td>${usuario.id}</td>
                                <td>${usuario.nome}</td>
                                <td>${usuario.email}</td>
                            </tr>`;
                        });
                    })
                    .catch(error => console.error("Erro ao buscar usuários:", error));
            }

            // Adicionar usuário
            document.getElementById("userForm").addEventListener("submit", function(event) {
                event.preventDefault();
                const formData = new FormData(this);
                const jsonData = Object.fromEntries(formData.entries());

                fetch(apiUrl, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify(jsonData)
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.mensagem || data.erro);
                    carregarUsuarios(); // Atualiza a lista
                })
                .catch(error => console.error("Erro ao adicionar usuário:", error));
            });

            // Inicializar a lista
            carregarUsuarios();
        });
    </script>
</body>
</html>